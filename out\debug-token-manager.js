// Minimal debug helper for Token Manager
// Ensures require('./debug-token-manager') succeeds and exposes showDebugInfo()

const vscode = require('vscode');

async function showDebugInfo() {
  try {
    const messageLines = [
      'Token Manager Debug Info',
      '— This is a minimal stub for debugging —',
    ];

    // Try to display an info message
    vscode.window.showInformationMessage(messageLines.join(' | '));
  } catch (error) {
    // Fallback to console if no window available
    console.log('[TokenManager][Debug] showDebugInfo error:', error);
  }
}

module.exports = { showDebugInfo };





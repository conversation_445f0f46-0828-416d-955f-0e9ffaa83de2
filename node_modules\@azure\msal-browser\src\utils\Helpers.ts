/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */

/**
 * Utility function to remove an element from an array in place.
 * @param array - The array from which to remove the element.
 * @param element - The element to remove from the array.
 */
export function removeElementFromArray(
    array: Array<string>,
    element: string
): void {
    const index = array.indexOf(element);
    if (index > -1) {
        array.splice(index, 1);
    }
}
